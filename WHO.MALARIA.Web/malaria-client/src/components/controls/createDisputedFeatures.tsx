import { disputedData } from "./disputedMapdata";

// Create GeoJSON features from disputedData
export const createDisputedFeatures = () => {
  const features: any = {
    type: "FeatureCollection",
    features: [],
  };

  Object.entries(disputedData).forEach(([name, regionData]: any) => {
    if (regionData.rings) {
      regionData.rings.forEach((ring: any, ringIndex: number) => {
        // Skip rings with insufficient coordinates for a polygon (need at least 3)
        if (ring.length < 3) {
          return;
        }

        // EXPERIMENTAL: Try swapping coordinates from [lat,lon] to [lon,lat]
        const geographicRing = ring.map((coord: [number, number]) => [
          coord[1],
          coord[0],
        ]);

        // Ensure the polygon ring is closed
        if (
          geographicRing.length > 0 &&
          (geographicRing[0][0] !==
            geographicRing[geographicRing.length - 1][0] ||
            geographicRing[0][1] !==
              geographicRing[geographicRing.length - 1][1])
        ) {
          geographicRing.push(geographicRing[0]);
        }

        const feature = {
          type: "Feature",
          properties: { NAME: name, disputed: true },
          geometry: {
            type: "Polygon",
            coordinates: [geographicRing],
          },
        };

        features.features.push(feature);
      });
    }

    if (regionData.paths) {
      regionData.paths.forEach((path: any, pathIndex: number) => {
        // Skip paths with insufficient coordinates for a line (need at least 2)
        if (path.length < 2) {
          return;
        }

        // EXPERIMENTAL: Try swapping coordinates from [lat,lon] to [lon,lat]
        const geographicPath = path.map((coord: [number, number]) => [
          coord[1],
          coord[0],
        ]);

        const feature = {
          type: "Feature",
          properties: { NAME: name, disputed: true },
          geometry: {
            type: "LineString",
            coordinates: geographicPath,
          },
        };

        features.features.push(feature);
      });
    }
  });

  return features;
};
