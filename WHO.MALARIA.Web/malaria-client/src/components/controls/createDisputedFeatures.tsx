import { disputedData } from "./disputedMapdata";

function webMercatorToLonLat([x, y]: [number, number]): [number, number] {
  const RADIUS = 6378137;

  const lon = (x / RADIUS) * (180 / Math.PI);
  const lat = (y / RADIUS) * (180 / Math.PI);
  return [
    lon,
    (180 / Math.PI) * (2 * Math.atan(Math.exp(y / RADIUS)) - Math.PI / 2),
  ];
}

// Check if coordinates are in Web Mercator format (large numbers) or already in lon/lat format
function isWebMercator([x, y]: [number, number]): boolean {
  // Web Mercator coordinates are typically much larger than lon/lat coordinates
  // Longitude ranges from -180 to 180, latitude from -90 to 90
  // Web Mercator coordinates are typically in the millions
  return Math.abs(x) > 1000 || Math.abs(y) > 1000;
}

// Create GeoJSON features from disputedData
export const createDisputedFeatures = () => {
  const features: any = {
    type: "FeatureCollection",
    features: [],
  };

  Object.entries(disputedData).forEach(([name, regionData]: any) => {
    if (regionData.rings) {
      regionData.rings.forEach((ring: any) => {
        // Check if coordinates need conversion or are already in lon/lat format
        const geographicRing = ring.map((coord: [number, number]) =>
          isWebMercator(coord) ? webMercatorToLonLat(coord) : coord
        );

        // Skip rings with insufficient coordinates for a polygon (need at least 3)
        if (geographicRing.length < 3) {
          return;
        }

        // Ensure the polygon ring is closed
        if (
          geographicRing.length > 0 &&
          (geographicRing[0][0] !==
            geographicRing[geographicRing.length - 1][0] ||
            geographicRing[0][1] !==
              geographicRing[geographicRing.length - 1][1])
        ) {
          geographicRing.push(geographicRing[0]);
        }

        features.features.push({
          type: "Feature",
          properties: { NAME: name, disputed: true },
          geometry: {
            type: "Polygon",
            coordinates: [geographicRing],
          },
        });
      });
    }

    if (regionData.paths) {
      regionData.paths.forEach((path: any) => {
        // Check if coordinates need conversion or are already in lon/lat format
        const geographicPath = path.map((coord: [number, number]) =>
          isWebMercator(coord) ? webMercatorToLonLat(coord) : coord
        );

        // Skip paths with insufficient coordinates for a line (need at least 2)
        if (geographicPath.length < 2) {
          return;
        }

        features.features.push({
          type: "Feature",
          properties: { NAME: name, disputed: true },
          geometry: {
            type: "LineString",
            coordinates: geographicPath,
          },
        });
      });
    }
  });

  return features;
};
