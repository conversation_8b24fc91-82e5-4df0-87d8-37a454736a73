// Simple test to verify disputed data is accessible
import { disputedData } from './components/controls/disputedMapdata.tsx';

console.log('=== TESTING DISPUTED DATA ACCESS ===');
console.log('disputedData type:', typeof disputedData);
console.log('disputedData keys:', Object.keys(disputedData));
console.log('First region:', Object.entries(disputedData)[0]);

// Test the createDisputedFeatures function
import { createDisputedFeatures } from './components/controls/createDisputedFeatures.tsx';

console.log('=== TESTING FEATURE CREATION ===');
const features = createDisputedFeatures();
console.log('Features result:', features);
console.log('Features count:', features.features?.length);

if (features.features?.length > 0) {
    console.log('First feature:', features.features[0]);
} else {
    console.error('No features created!');
}
